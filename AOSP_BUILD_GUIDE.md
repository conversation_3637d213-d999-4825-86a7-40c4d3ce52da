# AOSP lmkd.cpp 编译指南

## 🎯 **概述**
本指南将帮助您在 macOS ARM64 系统上设置 AOSP 构建环境并编译修改后的 lmkd.cpp。

## 📋 **系统要求**
- macOS 10.15+ (您的系统: macOS 15.5 ✅)
- 至少 250GB 可用磁盘空间
- 16GB+ RAM (推荐 32GB)
- 稳定的网络连接

## 🔧 **环境设置**

### 1. **安装必要工具**
```bash
# 安装 Xcode 命令行工具 (已完成 ✅)
xcode-select --install

# 安装 Homebrew (已安装 ✅)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装必要的包
brew install git-lfs
```

### 2. **设置 repo 工具**
```bash
# 创建 bin 目录并下载 repo
mkdir -p ~/bin
curl https://storage.googleapis.com/git-repo-downloads/repo > ~/bin/repo
chmod a+x ~/bin/repo

# 添加到 PATH (添加到 ~/.zshrc 或 ~/.bash_profile)
echo 'export PATH=~/bin:$PATH' >> ~/.zshrc
source ~/.zshrc
```

### 3. **配置 Git**
```bash
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
```

## 📥 **下载 AOSP 源码**

### 1. **创建工作目录**
```bash
mkdir -p ~/aosp
cd ~/aosp
```

### 2. **初始化 repo**
```bash
# 初始化 AOSP 主分支 (约 150GB)
repo init -u https://android.googlesource.com/platform/manifest -b main

# 或者选择特定版本 (推荐)
repo init -u https://android.googlesource.com/platform/manifest -b android-14.0.0_r1
```

### 3. **同步源码** (⚠️ 需要很长时间)
```bash
# 完整同步 (可能需要几小时到几天)
repo sync -c -j8

# 或者只同步 lmkd 相关模块
repo sync system/memory/lmkd
```

## 🎯 **仅编译 lmkd 的快速方法**

如果您只想编译 lmkd 而不下载完整 AOSP：

### 1. **克隆 lmkd 仓库**
```bash
git clone https://android.googlesource.com/platform/system/memory/lmkd
cd lmkd
```

### 2. **应用您的修改**
```bash
# 将您修改的 lmkd.cpp 复制到这里
cp /path/to/your/modified/lmkd.cpp ./lmkd.cpp
```

### 3. **创建简化的编译脚本**
```bash
# 创建 build_lmkd.sh
cat > build_lmkd.sh << 'EOF'
#!/bin/bash

# 设置编译环境
export ANDROID_BUILD_TOP=$(pwd)
export TARGET_PRODUCT=aosp_arm64
export TARGET_BUILD_VARIANT=eng

# 编译 lmkd
clang++ -o lmkd \
    -std=c++17 \
    -Wall -Wextra \
    -I. \
    -DANDROID \
    -D__ANDROID_API__=30 \
    lmkd.cpp \
    -lpthread -llog

echo "lmkd compiled successfully!"
EOF

chmod +x build_lmkd.sh
```

## 🏗️ **完整 AOSP 编译流程**

### 1. **设置构建环境**
```bash
cd ~/aosp
source build/envsetup.sh
```

### 2. **选择目标设备**
```bash
# 查看可用目标
lunch

# 选择通用 ARM64 目标
lunch aosp_arm64-eng
```

### 3. **应用您的修改**
```bash
# 找到 lmkd 源码位置
cd system/memory/lmkd

# 备份原文件
cp lmkd.cpp lmkd.cpp.orig

# 应用您的修改
cp /path/to/your/modified/lmkd.cpp ./lmkd.cpp
```

### 4. **编译 lmkd**
```bash
# 回到 AOSP 根目录
cd ~/aosp

# 编译单个模块
m lmkd

# 或者编译整个系统 (需要很长时间)
m -j$(nproc)
```

## 🔍 **验证编译结果**

### 1. **检查编译产物**
```bash
# 查找编译后的 lmkd
find out/ -name "lmkd" -type f

# 通常位置
ls -la out/target/product/generic_arm64/system/bin/lmkd
```

### 2. **测试二进制文件**
```bash
# 检查文件信息
file out/target/product/generic_arm64/system/bin/lmkd

# 检查依赖
otool -L out/target/product/generic_arm64/system/bin/lmkd  # macOS
```

## 🚀 **部署到设备**

### 1. **使用 ADB 推送**
```bash
# 推送到设备
adb push out/target/product/generic_arm64/system/bin/lmkd /system/bin/

# 设置权限
adb shell chmod 755 /system/bin/lmkd

# 重启 lmkd 服务
adb shell stop lmkd
adb shell start lmkd
```

### 2. **验证功能**
```bash
# 检查 lmkd 是否运行
adb shell ps | grep lmkd

# 查看日志
adb logcat | grep lmkd
```

## ⚠️ **注意事项**

1. **磁盘空间**: 完整 AOSP 需要 250GB+ 空间
2. **编译时间**: 首次编译可能需要几小时
3. **网络**: 源码下载需要稳定网络
4. **权限**: 部署到设备需要 root 权限
5. **兼容性**: 确保目标设备与编译版本匹配

## 🔧 **故障排除**

### 常见问题：
1. **编译错误**: 检查依赖库是否完整
2. **链接错误**: 确保所有 Android 库路径正确
3. **运行时错误**: 检查设备架构匹配
4. **权限问题**: 确保有 root 权限部署

## 📚 **参考资源**
- [AOSP 官方文档](https://source.android.com/setup/build/building)
- [Android 构建系统](https://source.android.com/setup/build)
- [lmkd 源码](https://android.googlesource.com/platform/system/memory/lmkd/)
