#ifndef _LIBLMKD_UTILS_H
#define _LIBLMKD_UTILS_H
#include <stdint.h>
#include <sys/types.h>

#define CTRL_PACKET_MAX_SIZE 256
typedef uint8_t LMKD_CTRL_PACKET[CTRL_PACKET_MAX_SIZE];

enum lmk_cmd {
    LMK_TARGET = 0,
    LMK_PROCPRIO,
    LMK_PROCREMOVE,
    LMK_PROCPURGE,
    LMK_GETKILLCNT,
    LMK_SUBSCRIBE,
    LMK_PROCKILL,
    LMK_UPDATE_PROPS
};

enum proc_type {
    PROC_TYPE_FIRST = 0,
    PROC_TYPE_APP = PROC_TYPE_FIRST,
    PROC_TYPE_SERVICE,
    PROC_TYPE_COUNT
};

enum lmk_async_event_type {
    LMK_ASYNC_EVENT_KILL = 0
};

enum update_props_result {
    UPDATE_PROPS_SUCCESS = 0,
    UPDATE_PROPS_SEND_ERR,
    UPDATE_PROPS_RECV_ERR,
    UPDATE_PROPS_FORMAT_ERR,
    UPDATE_PROPS_FAIL
};

struct lmk_procprio {
    pid_t pid;
    uid_t uid;
    int oomadj;
    int ptype;
};

struct lmk_procremove {
    pid_t pid;
};

struct lmk_subscribe {
    int evt_type;
};

struct lmk_target {
    int minfree;
    int oom_adj_score;
};

struct lmk_getkillcnt {
    int min_oomadj;
    int max_oomadj;
};

// 函数声明
enum lmk_cmd lmkd_pack_get_cmd(LMKD_CTRL_PACKET packet);
void lmkd_pack_get_procprio(LMKD_CTRL_PACKET packet, int field_count, struct lmk_procprio* params);
void lmkd_pack_get_procremove(LMKD_CTRL_PACKET packet, struct lmk_procremove* params);
void lmkd_pack_get_subscribe(LMKD_CTRL_PACKET packet, struct lmk_subscribe* params);
void lmkd_pack_get_target(LMKD_CTRL_PACKET packet, int target_idx, struct lmk_target* target);
void lmkd_pack_get_getkillcnt(LMKD_CTRL_PACKET packet, struct lmk_getkillcnt* params);
size_t lmkd_pack_set_prockills(LMKD_CTRL_PACKET packet, pid_t pid, uid_t uid);
size_t lmkd_pack_set_getkillcnt_repl(LMKD_CTRL_PACKET packet, int kill_cnt);
size_t lmkd_pack_set_update_props_repl(LMKD_CTRL_PACKET packet, int result);
int lmkd_connect(void);
enum update_props_result lmkd_update_props(int sock);

#endif
