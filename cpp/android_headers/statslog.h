#ifndef _STATSLOG_H
#define _STATSLOG_H
#include <stdint.h>
#include <sys/types.h>

enum kill_reasons {
    NONE = 0,
    LOW_MEMORY,
    PRESSURE_AFTER_KILL,
    NOT_RESPONDING
};

struct memory_stat {
    int64_t pgfault;
    int64_t pgmajfault;
    int64_t rss_in_bytes;
    int64_t cache_in_bytes;
    int64_t swap_in_bytes;
    int64_t process_start_time_ns;
    uint32_t min_oom_score;
    uint32_t rss_in_kb;
    uint32_t cache_in_kb;
    uint32_t swap_in_kb;
};

struct kill_stat {
    int32_t uid;
    char* taskname;
    enum kill_reasons kill_reason;
    int oom_score;
    int min_oom_score;
    int64_t free_mem_kb;
    int64_t free_swap_kb;
};

// 函数声明
void stats_store_taskname(int pid, const char* taskname);
void stats_remove_taskname(int pid);
void stats_purge_tasknames(void);
struct memory_stat* stats_read_memory_stat(bool per_app_memcg, int pid, uid_t uid, int64_t rss_kb, int64_t swap_kb);
void stats_write_lmk_kill_occurred(struct kill_stat* kill_st, struct memory_stat* mem_st);
void stats_write_lmk_kill_occurred_pid(pid_t pid, struct kill_stat* kill_st, struct memory_stat* mem_st);
void stats_write_lmk_state_changed(int state);

namespace android {
namespace lmkd {
namespace stats {
enum LmkStateChanged {
    LMK_STATE_CHANGED__STATE__START = 1,
    LMK_STATE_CHANGED__STATE__STOP = 2
};
}
}
}

#endif
