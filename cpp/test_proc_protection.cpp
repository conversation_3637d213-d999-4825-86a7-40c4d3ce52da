#include <iostream>
#include <cstring>
#include <ctime>
#include <cstdlib>

/* Process name protection settings */
#define PROC_NAME_PROTECTION_WINDOW_MS (10 * 60 * 1000)  /* 10 minutes */
#define PROC_NAME_PROTECTION_THRESHOLD 3                  /* 3 times */
#define PROC_NAME_CACHE_SIZE 20                          /* max 20 entries */
#define PROC_NAME_HASH_SIZE 32                           /* hash table size */

/* Process name protection structures */
struct proc_name_entry {
    char name[256];                    /* process name */
    int kill_count;                    /* number of times killed */
    struct timespec first_kill_time;   /* time of first kill */
    struct timespec last_kill_time;    /* time of last kill */
    struct proc_name_entry *hash_next; /* next in hash chain */
    struct proc_name_entry *lru_prev;  /* LRU list previous */
    struct proc_name_entry *lru_next;  /* LRU list next */
};

struct proc_name_cache {
    struct proc_name_entry *hash_table[PROC_NAME_HASH_SIZE];
    struct proc_name_entry *lru_head;  /* LRU list head (most recent) */
    struct proc_name_entry *lru_tail;  /* LRU list tail (least recent) */
    struct proc_name_entry entries[PROC_NAME_CACHE_SIZE];
    int entry_count;
};

/* Process name protection cache */
static struct proc_name_cache proc_name_cache = {0};
static bool enable_proc_name_protection = true;

static long get_time_diff_ms(struct timespec *from, struct timespec *to) {
    return (to->tv_sec - from->tv_sec) * 1000L +
           (to->tv_nsec - from->tv_nsec) / 1000000L;
}

static void init_proc_name_cache() {
    memset(&proc_name_cache, 0, sizeof(proc_name_cache));
    proc_name_cache.lru_head = NULL;
    proc_name_cache.lru_tail = NULL;
    proc_name_cache.entry_count = 0;
}

static unsigned int proc_name_hash(const char *name) {
    unsigned int hash = 5381;
    int c;
    while ((c = *name++)) {
        hash = ((hash << 5) + hash) + c; /* hash * 33 + c */
    }
    return hash % PROC_NAME_HASH_SIZE;
}

static struct proc_name_entry *find_proc_name_entry(const char *name) {
    unsigned int hash = proc_name_hash(name);
    struct proc_name_entry *entry = proc_name_cache.hash_table[hash];
    
    while (entry) {
        if (strcmp(entry->name, name) == 0) {
            return entry;
        }
        entry = entry->hash_next;
    }
    return NULL;
}

static void remove_proc_name_entry_from_lru(struct proc_name_entry *entry) {
    if (entry->lru_prev) {
        entry->lru_prev->lru_next = entry->lru_next;
    } else {
        proc_name_cache.lru_head = entry->lru_next;
    }
    
    if (entry->lru_next) {
        entry->lru_next->lru_prev = entry->lru_prev;
    } else {
        proc_name_cache.lru_tail = entry->lru_prev;
    }
    
    entry->lru_prev = NULL;
    entry->lru_next = NULL;
}

static void add_proc_name_entry_to_lru_head(struct proc_name_entry *entry) {
    entry->lru_prev = NULL;
    entry->lru_next = proc_name_cache.lru_head;
    
    if (proc_name_cache.lru_head) {
        proc_name_cache.lru_head->lru_prev = entry;
    } else {
        proc_name_cache.lru_tail = entry;
    }
    
    proc_name_cache.lru_head = entry;
}

static void cleanup_expired_proc_name_entries(struct timespec *current_time) {
    struct proc_name_entry *entry = proc_name_cache.lru_tail;
    
    while (entry) {
        struct proc_name_entry *prev = entry->lru_prev;
        long time_diff_ms = get_time_diff_ms(&entry->first_kill_time, current_time);
        
        if (time_diff_ms > PROC_NAME_PROTECTION_WINDOW_MS) {
            /* Remove from hash table */
            unsigned int hash = proc_name_hash(entry->name);
            struct proc_name_entry **hash_entry = &proc_name_cache.hash_table[hash];
            
            while (*hash_entry && *hash_entry != entry) {
                hash_entry = &(*hash_entry)->hash_next;
            }
            if (*hash_entry) {
                *hash_entry = entry->hash_next;
            }
            
            /* Remove from LRU */
            remove_proc_name_entry_from_lru(entry);
            
            /* Clear entry */
            memset(entry, 0, sizeof(*entry));
            proc_name_cache.entry_count--;
        }
        
        entry = prev;
    }
}

static bool should_protect_process(const char *proc_name, struct timespec *current_time) {
    if (!enable_proc_name_protection || !proc_name || strlen(proc_name) == 0) {
        return false;
    }
    
    cleanup_expired_proc_name_entries(current_time);
    
    struct proc_name_entry *entry = find_proc_name_entry(proc_name);
    if (!entry) {
        return false;
    }
    
    /* Check if process has been killed too frequently */
    if (entry->kill_count >= PROC_NAME_PROTECTION_THRESHOLD) {
        long time_diff_ms = get_time_diff_ms(&entry->first_kill_time, current_time);
        if (time_diff_ms <= PROC_NAME_PROTECTION_WINDOW_MS) {
            std::cout << "Protecting process '" << proc_name << "' (killed " 
                      << entry->kill_count << " times in " << time_diff_ms << " ms)" << std::endl;
            return true;
        }
    }
    
    return false;
}

static void record_process_kill(const char *proc_name, struct timespec *current_time) {
    if (!enable_proc_name_protection || !proc_name || strlen(proc_name) == 0) {
        return;
    }
    
    cleanup_expired_proc_name_entries(current_time);
    
    struct proc_name_entry *entry = find_proc_name_entry(proc_name);
    
    if (entry) {
        /* Update existing entry */
        entry->kill_count++;
        entry->last_kill_time = *current_time;
        
        /* Move to LRU head */
        remove_proc_name_entry_from_lru(entry);
        add_proc_name_entry_to_lru_head(entry);
        
        std::cout << "Updated kill count for '" << proc_name << "' to " << entry->kill_count << std::endl;
    } else {
        /* Create new entry */
        if (proc_name_cache.entry_count >= PROC_NAME_CACHE_SIZE) {
            /* Remove LRU tail to make space */
            struct proc_name_entry *lru_tail = proc_name_cache.lru_tail;
            if (lru_tail) {
                std::cout << "Evicting LRU entry: " << lru_tail->name << std::endl;
                
                /* Remove from hash table */
                unsigned int hash = proc_name_hash(lru_tail->name);
                struct proc_name_entry **hash_entry = &proc_name_cache.hash_table[hash];
                
                while (*hash_entry && *hash_entry != lru_tail) {
                    hash_entry = &(*hash_entry)->hash_next;
                }
                if (*hash_entry) {
                    *hash_entry = lru_tail->hash_next;
                }
                
                /* Remove from LRU */
                remove_proc_name_entry_from_lru(lru_tail);
                
                /* Reuse this entry */
                entry = lru_tail;
                memset(entry, 0, sizeof(*entry));
                proc_name_cache.entry_count--;
            }
        }
        
        if (!entry) {
            /* Find free entry */
            for (int i = 0; i < PROC_NAME_CACHE_SIZE; i++) {
                if (proc_name_cache.entries[i].name[0] == '\0') {
                    entry = &proc_name_cache.entries[i];
                    break;
                }
            }
        }
        
        if (entry) {
            /* Initialize new entry */
            strncpy(entry->name, proc_name, sizeof(entry->name) - 1);
            entry->name[sizeof(entry->name) - 1] = '\0';
            entry->kill_count = 1;
            entry->first_kill_time = *current_time;
            entry->last_kill_time = *current_time;
            entry->hash_next = NULL;
            
            /* Add to hash table */
            unsigned int hash = proc_name_hash(proc_name);
            entry->hash_next = proc_name_cache.hash_table[hash];
            proc_name_cache.hash_table[hash] = entry;
            
            /* Add to LRU head */
            add_proc_name_entry_to_lru_head(entry);
            
            proc_name_cache.entry_count++;
            
            std::cout << "Created new entry for '" << proc_name << "'" << std::endl;
        }
    }
}

int main() {
    init_proc_name_cache();
    
    struct timespec current_time;
    clock_gettime(CLOCK_MONOTONIC, &current_time);
    
    std::cout << "Testing process name protection..." << std::endl;
    
    // Test 1: Record kills for same process
    std::cout << "\n=== Test 1: Recording kills for same process ===" << std::endl;
    record_process_kill("com.example.app", &current_time);
    record_process_kill("com.example.app", &current_time);
    record_process_kill("com.example.app", &current_time);
    
    // Test 2: Check if process should be protected
    std::cout << "\n=== Test 2: Checking protection ===" << std::endl;
    bool protected1 = should_protect_process("com.example.app", &current_time);
    std::cout << "Should protect com.example.app: " << (protected1 ? "YES" : "NO") << std::endl;
    
    bool protected2 = should_protect_process("com.other.app", &current_time);
    std::cout << "Should protect com.other.app: " << (protected2 ? "YES" : "NO") << std::endl;
    
    // Test 3: Test LRU eviction
    std::cout << "\n=== Test 3: Testing LRU eviction ===" << std::endl;
    for (int i = 0; i < 25; i++) {
        char app_name[64];
        snprintf(app_name, sizeof(app_name), "app%d", i);
        record_process_kill(app_name, &current_time);
    }
    
    std::cout << "\nCache entry count: " << proc_name_cache.entry_count << std::endl;
    
    return 0;
}
