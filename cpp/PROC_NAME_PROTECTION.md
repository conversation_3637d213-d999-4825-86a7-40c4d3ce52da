# 进程名保护功能实现文档

## 功能概述

为 lmkd.cpp 添加了"避免查杀10分钟内进程名出现3次以及以上的进程"功能，防止频繁重启的进程被反复查杀。

## 设计要求

1. **LRU缓存**: 最多记录20条进程信息，使用LRU算法管理缓存
2. **哈希表**: 根据进程名做哈希，快速定位进程记录
3. **时间窗口**: 10分钟内的查杀记录
4. **阈值**: 进程名出现3次及以上时进行保护

## 实现细节

### 数据结构

```c
struct proc_name_entry {
    char name[256];                    /* 进程名 */
    int kill_count;                    /* 查杀次数 */
    struct timespec first_kill_time;   /* 首次查杀时间 */
    struct timespec last_kill_time;    /* 最后查杀时间 */
    struct proc_name_entry *hash_next; /* 哈希链表下一个节点 */
    struct proc_name_entry *lru_prev;  /* LRU链表前一个节点 */
    struct proc_name_entry *lru_next;  /* LRU链表下一个节点 */
};

struct proc_name_cache {
    struct proc_name_entry *hash_table[PROC_NAME_HASH_SIZE];  /* 哈希表 */
    struct proc_name_entry *lru_head;   /* LRU链表头(最新) */
    struct proc_name_entry *lru_tail;   /* LRU链表尾(最旧) */
    struct proc_name_entry entries[PROC_NAME_CACHE_SIZE];     /* 条目数组 */
    int entry_count;                    /* 当前条目数量 */
};
```

### 配置参数

```c
#define PROC_NAME_PROTECTION_WINDOW_MS (10 * 60 * 1000)  /* 10分钟时间窗口 */
#define PROC_NAME_PROTECTION_THRESHOLD 3                  /* 保护阈值: 3次 */
#define PROC_NAME_CACHE_SIZE 20                          /* 最大缓存条目数 */
#define PROC_NAME_HASH_SIZE 32                           /* 哈希表大小 */
```

### 核心函数

1. **init_proc_name_cache()**: 初始化进程名缓存
2. **should_protect_process()**: 检查进程是否应该被保护
3. **record_process_kill()**: 记录进程查杀事件
4. **cleanup_expired_proc_name_entries()**: 清理过期条目

### 哈希算法

使用 djb2 哈希算法:
```c
static unsigned int proc_name_hash(const char *name) {
    unsigned int hash = 5381;
    int c;
    while ((c = *name++)) {
        hash = ((hash << 5) + hash) + c; /* hash * 33 + c */
    }
    return hash % PROC_NAME_HASH_SIZE;
}
```

### LRU管理

- **新增条目**: 添加到LRU链表头部
- **访问条目**: 移动到LRU链表头部
- **缓存满时**: 移除LRU链表尾部的条目

## 集成点

### 1. kill_one_process() 函数修改

在获取进程名后、实际查杀前添加保护检查:

```c
/* Check if this process should be protected from killing */
if (should_protect_process(taskname, tm)) {
    ALOGI("Process '%s' (%d) protected from killing due to frequent restarts", taskname, pid);
    goto out;
}
```

在成功查杀后记录事件:

```c
/* Record this kill for process protection */
record_process_kill(taskname, tm);
```

### 2. 初始化

在 init() 函数中添加缓存初始化:

```c
/* Initialize process name protection cache */
init_proc_name_cache();
```

### 3. 配置支持

添加系统属性支持:

```c
enable_proc_name_protection = property_get_bool("ro.lmk.proc_name_protection", true);
```

## 工作流程

1. **进程查杀时**:
   - 获取进程名
   - 调用 `should_protect_process()` 检查是否需要保护
   - 如果需要保护，跳过查杀并记录日志
   - 如果不需要保护，执行查杀
   - 查杀成功后调用 `record_process_kill()` 记录事件

2. **保护检查逻辑**:
   - 清理过期条目(超过10分钟)
   - 查找进程名对应的条目
   - 检查查杀次数是否达到阈值(3次)
   - 检查是否在时间窗口内(10分钟)

3. **记录查杀事件**:
   - 清理过期条目
   - 查找或创建进程名条目
   - 更新查杀次数和时间戳
   - 更新LRU位置

## 性能考虑

1. **哈希表**: O(1) 平均查找时间
2. **LRU操作**: O(1) 时间复杂度
3. **内存使用**: 固定大小缓存，最大约 20 * 300 字节
4. **过期清理**: 在每次操作时进行，避免定时器开销

## 配置选项

- `ro.lmk.proc_name_protection`: 启用/禁用进程名保护功能 (默认: true)
- 可以通过修改宏定义调整时间窗口、阈值和缓存大小

## 日志输出

- 保护进程时: "Process 'xxx' (pid) protected from killing due to frequent restarts"
- 调试模式下会输出更详细的缓存操作信息

## 测试

提供了 `test_proc_protection.cpp` 测试程序，验证:
- 基本的记录和保护功能
- LRU缓存管理
- 哈希表冲突处理
- 过期条目清理
