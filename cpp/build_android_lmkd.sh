#!/bin/bash

# Android lmkd 本地编译脚本
# 适用于 macOS ARM64 系统

set -e

echo "🚀 开始编译 Android lmkd..."

# 检查必要工具
check_tools() {
    echo "📋 检查编译工具..."
    
    if ! command -v clang++ &> /dev/null; then
        echo "❌ clang++ 未找到，请安装 Xcode 命令行工具"
        exit 1
    fi
    
    echo "✅ clang++ 已安装: $(clang++ --version | head -1)"
}

# 创建 Android 头文件模拟
create_android_headers() {
    echo "📁 创建 Android 头文件模拟..."
    
    mkdir -p android_headers/{cutils,log,private,psi,system,liblmkd_utils}
    
    # 创建基本的 Android 头文件模拟
    cat > android_headers/cutils/properties.h << 'EOF'
#ifndef _CUTILS_PROPERTIES_H
#define _CUTILS_PROPERTIES_H
#include <stdbool.h>
#define PROPERTY_VALUE_MAX 256
int property_get_int32(const char *key, int default_value);
bool property_get_bool(const char *key, bool default_value);
int property_set(const char *key, const char *value);
#endif
EOF

    cat > android_headers/cutils/sched_policy.h << 'EOF'
#ifndef _CUTILS_SCHED_POLICY_H
#define _CUTILS_SCHED_POLICY_H
typedef enum {
    SP_BACKGROUND = 0,
    SP_FOREGROUND = 1,
} SchedPolicy;
int set_process_group_and_prio(int pid, SchedPolicy policy, int priority);
#endif
EOF

    cat > android_headers/cutils/sockets.h << 'EOF'
#ifndef _CUTILS_SOCKETS_H
#define _CUTILS_SOCKETS_H
int android_get_control_socket(const char* name);
#endif
EOF

    cat > android_headers/cutils/trace.h << 'EOF'
#ifndef _CUTILS_TRACE_H
#define _CUTILS_TRACE_H
#define ATRACE_INT(name, value) do {} while(0)
#endif
EOF

    cat > android_headers/log/log.h << 'EOF'
#ifndef _LOG_LOG_H
#define _LOG_LOG_H
#include <stdio.h>
#define ALOGE(fmt, ...) printf("E: " fmt "\n", ##__VA_ARGS__)
#define ALOGW(fmt, ...) printf("W: " fmt "\n", ##__VA_ARGS__)
#define ALOGI(fmt, ...) printf("I: " fmt "\n", ##__VA_ARGS__)
#define ALOGD(fmt, ...) printf("D: " fmt "\n", ##__VA_ARGS__)
#endif
EOF

    cat > android_headers/log/log_event_list.h << 'EOF'
#ifndef _LOG_LOG_EVENT_LIST_H
#define _LOG_LOG_EVENT_LIST_H
#include <stdint.h>
typedef void* android_log_context;
#define LOG_ID_EVENTS 0
android_log_context create_android_logger(int tag);
int android_log_write_int32(android_log_context ctx, int32_t value);
int android_log_write_list(android_log_context ctx, int id);
int android_log_reset(android_log_context ctx);
#endif
EOF

    cat > android_headers/log/log_time.h << 'EOF'
#ifndef _LOG_LOG_TIME_H
#define _LOG_LOG_TIME_H
// 空实现
#endif
EOF

    cat > android_headers/private/android_filesystem_config.h << 'EOF'
#ifndef _PRIVATE_ANDROID_FILESYSTEM_CONFIG_H
#define _PRIVATE_ANDROID_FILESYSTEM_CONFIG_H
#define AID_SYSTEM 1000
#define AID_READPROC 3009
#endif
EOF

    cat > android_headers/psi/psi.h << 'EOF'
#ifndef _PSI_PSI_H
#define _PSI_PSI_H
enum psi_stall_type {
    PSI_SOME = 0,
    PSI_FULL = 1
};
int init_psi_monitor(enum psi_stall_type type, int threshold_us, int window_us);
int register_psi_monitor(int epollfd, int fd, void* data);
void destroy_psi_monitor(int fd);
#endif
EOF

    cat > android_headers/system/thread_defs.h << 'EOF'
#ifndef _SYSTEM_THREAD_DEFS_H
#define _SYSTEM_THREAD_DEFS_H
#define ANDROID_PRIORITY_HIGHEST -20
#endif
EOF

    cat > android_headers/liblmkd_utils.h << 'EOF'
#ifndef _LIBLMKD_UTILS_H
#define _LIBLMKD_UTILS_H
#include <stdint.h>
#include <sys/types.h>

#define CTRL_PACKET_MAX_SIZE 256
typedef uint8_t LMKD_CTRL_PACKET[CTRL_PACKET_MAX_SIZE];

enum lmk_cmd {
    LMK_TARGET = 0,
    LMK_PROCPRIO,
    LMK_PROCREMOVE,
    LMK_PROCPURGE,
    LMK_GETKILLCNT,
    LMK_SUBSCRIBE,
    LMK_PROCKILL,
    LMK_UPDATE_PROPS
};

enum proc_type {
    PROC_TYPE_FIRST = 0,
    PROC_TYPE_APP = PROC_TYPE_FIRST,
    PROC_TYPE_SERVICE,
    PROC_TYPE_COUNT
};

enum lmk_async_event_type {
    LMK_ASYNC_EVENT_KILL = 0
};

enum update_props_result {
    UPDATE_PROPS_SUCCESS = 0,
    UPDATE_PROPS_SEND_ERR,
    UPDATE_PROPS_RECV_ERR,
    UPDATE_PROPS_FORMAT_ERR,
    UPDATE_PROPS_FAIL
};

struct lmk_procprio {
    pid_t pid;
    uid_t uid;
    int oomadj;
    int ptype;
};

struct lmk_procremove {
    pid_t pid;
};

struct lmk_subscribe {
    int evt_type;
};

struct lmk_target {
    int minfree;
    int oom_adj_score;
};

struct lmk_getkillcnt {
    int min_oomadj;
    int max_oomadj;
};

// 函数声明
enum lmk_cmd lmkd_pack_get_cmd(LMKD_CTRL_PACKET packet);
void lmkd_pack_get_procprio(LMKD_CTRL_PACKET packet, int field_count, struct lmk_procprio* params);
void lmkd_pack_get_procremove(LMKD_CTRL_PACKET packet, struct lmk_procremove* params);
void lmkd_pack_get_subscribe(LMKD_CTRL_PACKET packet, struct lmk_subscribe* params);
void lmkd_pack_get_target(LMKD_CTRL_PACKET packet, int target_idx, struct lmk_target* target);
void lmkd_pack_get_getkillcnt(LMKD_CTRL_PACKET packet, struct lmk_getkillcnt* params);
size_t lmkd_pack_set_prockills(LMKD_CTRL_PACKET packet, pid_t pid, uid_t uid);
size_t lmkd_pack_set_getkillcnt_repl(LMKD_CTRL_PACKET packet, int kill_cnt);
size_t lmkd_pack_set_update_props_repl(LMKD_CTRL_PACKET packet, int result);
int lmkd_connect(void);
enum update_props_result lmkd_update_props(int sock);

#endif
EOF

    cat > android_headers/lmkd.h << 'EOF'
#ifndef _LMKD_H
#define _LMKD_H
#define MAX_TARGETS 6
#endif
EOF

    cat > android_headers/statslog_lmkd.h << 'EOF'
#ifndef _STATSLOG_LMKD_H
#define _STATSLOG_LMKD_H
// 空实现
#endif
EOF

    # 添加 sys/epoll.h 模拟 (macOS 兼容)
    mkdir -p android_headers/sys
    cat > android_headers/sys/epoll.h << 'EOF'
#ifndef _SYS_EPOLL_H
#define _SYS_EPOLL_H
#include <sys/event.h>
#include <stdint.h>

#define EPOLLIN     0x001
#define EPOLLOUT    0x004
#define EPOLLERR    0x008
#define EPOLLHUP    0x010

#define EPOLL_CTL_ADD 1
#define EPOLL_CTL_DEL 2
#define EPOLL_CTL_MOD 3

typedef union epoll_data {
    void *ptr;
    int fd;
    uint32_t u32;
    uint64_t u64;
} epoll_data_t;

struct epoll_event {
    uint32_t events;
    epoll_data_t data;
};

int epoll_create(int size);
int epoll_ctl(int epfd, int op, int fd, struct epoll_event *event);
int epoll_wait(int epfd, struct epoll_event *events, int maxevents, int timeout);
#endif
EOF

    # 添加 sys/pidfd.h 模拟
    cat > android_headers/sys/pidfd.h << 'EOF'
#ifndef _SYS_PIDFD_H
#define _SYS_PIDFD_H
#include <signal.h>
#include <sys/types.h>
int pidfd_open(pid_t pid, unsigned int flags);
int pidfd_send_signal(int pidfd, int sig, siginfo_t *info, unsigned int flags);
#endif
EOF

    # 添加 sys/eventfd.h 模拟
    cat > android_headers/sys/eventfd.h << 'EOF'
#ifndef _SYS_EVENTFD_H
#define _SYS_EVENTFD_H
#include <stdint.h>

#define EFD_CLOEXEC 02000000
#define EFD_NONBLOCK 04000

typedef uint64_t eventfd_t;

int eventfd(unsigned int initval, int flags);
#endif
EOF

    # 添加 sys/sysinfo.h 模拟
    cat > android_headers/sys/sysinfo.h << 'EOF'
#ifndef _SYS_SYSINFO_H
#define _SYS_SYSINFO_H
struct sysinfo {
    long uptime;
    unsigned long loads[3];
    unsigned long totalram;
    unsigned long freeram;
    unsigned long sharedram;
    unsigned long bufferram;
    unsigned long totalswap;
    unsigned long freeswap;
    unsigned short procs;
    unsigned long totalhigh;
    unsigned long freehigh;
    unsigned int mem_unit;
};
int sysinfo(struct sysinfo *info);
#endif
EOF

    # 添加 sys/syscall.h 模拟
    cat > android_headers/sys/syscall.h << 'EOF'
#ifndef _SYS_SYSCALL_H
#define _SYS_SYSCALL_H
#include <sys/syscall.h>
// 使用系统的 syscall.h
#endif
EOF

    cat > android_headers/statslog.h << 'EOF'
#ifndef _STATSLOG_H
#define _STATSLOG_H
#include <stdint.h>
#include <sys/types.h>

enum kill_reasons {
    NONE = 0,
    LOW_MEMORY,
    PRESSURE_AFTER_KILL,
    NOT_RESPONDING
};

struct memory_stat {
    int64_t pgfault;
    int64_t pgmajfault;
    int64_t rss_in_bytes;
    int64_t cache_in_bytes;
    int64_t swap_in_bytes;
    int64_t process_start_time_ns;
    uint32_t min_oom_score;
    uint32_t rss_in_kb;
    uint32_t cache_in_kb;
    uint32_t swap_in_kb;
};

struct kill_stat {
    int32_t uid;
    char* taskname;
    enum kill_reasons kill_reason;
    int oom_score;
    int min_oom_score;
    int64_t free_mem_kb;
    int64_t free_swap_kb;
};

// 函数声明
void stats_store_taskname(int pid, const char* taskname);
void stats_remove_taskname(int pid);
void stats_purge_tasknames(void);
struct memory_stat* stats_read_memory_stat(bool per_app_memcg, int pid, uid_t uid, int64_t rss_kb, int64_t swap_kb);
void stats_write_lmk_kill_occurred(struct kill_stat* kill_st, struct memory_stat* mem_st);
void stats_write_lmk_kill_occurred_pid(pid_t pid, struct kill_stat* kill_st, struct memory_stat* mem_st);
void stats_write_lmk_state_changed(int state);

namespace android {
namespace lmkd {
namespace stats {
enum LmkStateChanged {
    LMK_STATE_CHANGED__STATE__START = 1,
    LMK_STATE_CHANGED__STATE__STOP = 2
};
}
}
}

#endif
EOF

    echo "✅ Android 头文件模拟创建完成"
}

# 创建库函数模拟实现
create_android_libs() {
    echo "📚 创建 Android 库函数模拟..."
    
    cat > android_stubs.cpp << 'EOF'
#include "android_headers/cutils/properties.h"
#include "android_headers/cutils/sched_policy.h"
#include "android_headers/cutils/sockets.h"
#include "android_headers/log/log_event_list.h"
#include "android_headers/liblmkd_utils.h"
#include "android_headers/statslog.h"
#include "android_headers/psi/psi.h"
#include "android_headers/sys/epoll.h"
#include "android_headers/sys/pidfd.h"
#include "android_headers/sys/eventfd.h"
#include "android_headers/sys/sysinfo.h"
#include <cstring>
#include <unistd.h>
#include <signal.h>

// Property functions
int property_get_int32(const char *key, int default_value) {
    return default_value;
}

bool property_get_bool(const char *key, bool default_value) {
    return default_value;
}

int property_set(const char *key, const char *value) {
    return 0;
}

// Scheduler functions
int set_process_group_and_prio(int pid, SchedPolicy policy, int priority) {
    return 0;
}

// Socket functions
int android_get_control_socket(const char* name) {
    return -1;
}

// Log functions
android_log_context create_android_logger(int tag) {
    return nullptr;
}

int android_log_write_int32(android_log_context ctx, int32_t value) {
    return 0;
}

int android_log_write_list(android_log_context ctx, int id) {
    return 0;
}

int android_log_reset(android_log_context ctx) {
    return 0;
}

// PSI functions
int init_psi_monitor(enum psi_stall_type type, int threshold_us, int window_us) {
    return -1;
}

int register_psi_monitor(int epollfd, int fd, void* data) {
    return -1;
}

void destroy_psi_monitor(int fd) {
}

// Stats functions
void stats_store_taskname(int pid, const char* taskname) {
}

void stats_remove_taskname(int pid) {
}

void stats_purge_tasknames(void) {
}

struct memory_stat* stats_read_memory_stat(bool per_app_memcg, int pid, uid_t uid, int64_t rss_kb, int64_t swap_kb) {
    static struct memory_stat stat = {0};
    return &stat;
}

void stats_write_lmk_kill_occurred(struct kill_stat* kill_st, struct memory_stat* mem_st) {
}

void stats_write_lmk_kill_occurred_pid(pid_t pid, struct kill_stat* kill_st, struct memory_stat* mem_st) {
}

void stats_write_lmk_state_changed(int state) {
}

// LMKD utils functions
enum lmk_cmd lmkd_pack_get_cmd(LMKD_CTRL_PACKET packet) {
    return LMK_TARGET;
}

void lmkd_pack_get_procprio(LMKD_CTRL_PACKET packet, int field_count, struct lmk_procprio* params) {
    memset(params, 0, sizeof(*params));
}

void lmkd_pack_get_procremove(LMKD_CTRL_PACKET packet, struct lmk_procremove* params) {
    memset(params, 0, sizeof(*params));
}

void lmkd_pack_get_subscribe(LMKD_CTRL_PACKET packet, struct lmk_subscribe* params) {
    memset(params, 0, sizeof(*params));
}

void lmkd_pack_get_target(LMKD_CTRL_PACKET packet, int target_idx, struct lmk_target* target) {
    memset(target, 0, sizeof(*target));
}

void lmkd_pack_get_getkillcnt(LMKD_CTRL_PACKET packet, struct lmk_getkillcnt* params) {
    memset(params, 0, sizeof(*params));
}

size_t lmkd_pack_set_prockills(LMKD_CTRL_PACKET packet, pid_t pid, uid_t uid) {
    return 0;
}

size_t lmkd_pack_set_getkillcnt_repl(LMKD_CTRL_PACKET packet, int kill_cnt) {
    return 0;
}

size_t lmkd_pack_set_update_props_repl(LMKD_CTRL_PACKET packet, int result) {
    return 0;
}

int lmkd_connect(void) {
    return -1;
}

enum update_props_result lmkd_update_props(int sock) {
    return UPDATE_PROPS_FAIL;
}

// epoll functions (macOS stub implementation)
int epoll_create(int size) {
    return kqueue();
}

int epoll_ctl(int epfd, int op, int fd, struct epoll_event *event) {
    return 0; // stub
}

int epoll_wait(int epfd, struct epoll_event *events, int maxevents, int timeout) {
    return 0; // stub
}

// Additional required functions
int pidfd_open(pid_t pid, unsigned int flags) {
    return -1;
}

int pidfd_send_signal(int pidfd, int sig, siginfo_t *info, unsigned int flags) {
    return -1;
}

// eventfd functions
int eventfd(unsigned int initval, int flags) {
    return -1; // stub
}

// sysinfo functions
int sysinfo(struct sysinfo *info) {
    if (info) {
        memset(info, 0, sizeof(*info));
    }
    return 0;
}
EOF

    echo "✅ Android 库函数模拟创建完成"
}

# 编译函数
compile_lmkd() {
    echo "🔨 开始编译 lmkd..."

    if clang++ -o lmkd_android \
        -std=c++17 \
        -Wall -Wextra -Wno-unused-parameter -Wno-unused-variable \
        -I./android_headers \
        -DANDROID \
        -D__ANDROID_API__=30 \
        -DPAGE_SIZE=4096 \
        -DMS_PER_SEC=1000 \
        -DNS_PER_SEC=1000000000LL \
        -DNS_PER_MS=1000000LL \
        -DUS_PER_SEC=1000000LL \
        -DUS_PER_MS=1000LL \
        -DPATH_MAX=1024 \
        -DLINE_MAX=2048 \
        -DTEMP_FAILURE_RETRY\(exp\)=\(\{typeof\(exp\)\ _rc\;\ do\ \{\ _rc\ =\ \(exp\)\;\ \}\ while\ \(_rc\ ==\ -1\ \&\&\ errno\ ==\ EINTR\)\;\ _rc\;\ \}\) \
        lmkd.cpp android_stubs.cpp \
        -lpthread; then
        echo "✅ lmkd 编译成功！"
        echo "📁 输出文件: ./lmkd_android"
        ls -la lmkd_android
    else
        echo "❌ 编译失败"
        exit 1
    fi
}

# 主函数
main() {
    check_tools
    create_android_headers
    create_android_libs
    compile_lmkd
    
    echo ""
    echo "🎉 编译完成！"
    echo "📋 使用方法:"
    echo "   ./lmkd_android --help"
    echo ""
    echo "⚠️  注意: 这是模拟编译，某些 Android 特定功能可能无法正常工作"
    echo "   如需完整功能，请在真实的 AOSP 环境中编译"
}

# 执行主函数
main "$@"
